<!-- Primary camera frame - Main OpenVidu session -->
<div class="primary-camera-frame">
  <opv-session
    #ovSessionComponent
    [sessionName]="mySessionId"
    [user]="myUserName"
    [tokens]="tokens"
    [ovSettings]="ovSettings"
    (sessionCreated)="handlerSessionCreatedEvent($event)"
    (participantCreated)="handlerPublisherCreatedEvent($event)"
    (error)="handlerErrorEvent($event)"
    *ngIf="session && sessionJoined">
  </opv-session>
</div>

<!-- Secondary camera frame - Patient secondary -->
<div class="secondary-camera-frame" *ngIf="isSecondaryCameraAvailable">
  <div #secondaryContainer class="secondary-container">
    <!-- Secondary camera stream will be appended here -->
  </div>
</div>





