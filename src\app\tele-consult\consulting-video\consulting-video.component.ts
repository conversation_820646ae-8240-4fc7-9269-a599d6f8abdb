import { Component, Input, OnInit, OnDestroy, ViewChild, Output, EventEmitter, ElementRef } from '@angular/core';
import { throwError as observableThrowError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {
  OpenviduSessionComponent,
  Session,
  UserModel,
  OpenViduLayout,
  OvSettings,
  OpenViduLayoutOptions,
  Publisher,
  OpenVidu,
  Device
} from 'openvidu-angular';
import { TeleConsultService } from '../tele-consult.service';
import * as Settings from '../../config/settings';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-consulting-video',
  templateUrl: './consulting-video.component.html',
  styleUrls: ['./consulting-video.component.css']
})
export class ConsultingVideoComponent implements OnInit, OnDestroy {
  // OpenVidu server configuration
  OPENVIDU_SERVER_URL = Settings.OPENVIDU_SERVER_URL;
  OPENVIDU_SERVER_SECRET = Settings.OPENVIDU_SECRET;

  // Session properties
  mySessionId: string;
  myUserName = 'Participant' + Math.floor(Math.random() * 100);
  tokens: string[] = [];
  deviceTokens: string[] = [];
  session = false;
  sessionJoined = false;

  // OpenVidu objects
  ovSession: Session;
  ovLocalUsers: UserModel[];
  ovLayout: OpenViduLayout;
  ovLayoutOptions: OpenViduLayoutOptions;
  ovSettings: OvSettings;

  // Component inputs/outputs
  @Input() consultationId: any;
  @Input() participantName: any;
  @Input() closeVideoSession: any;
  @Output() showJoinButton: EventEmitter<boolean> = new EventEmitter();
  @ViewChild('ovSessionComponent') public ovSessionComponent: OpenviduSessionComponent;
  @ViewChild('secondaryContainer') secondaryContainer: ElementRef;

  // Video/audio properties
  videoToken = '';
  appointmentId = '';
  videoDevices: Device[] = [];
  audioDevices: Device[] = [];

  // Secondary camera properties
  secondaryOV: OpenVidu | null = null;
  secondarySession: Session | null = null;
  secondaryPublisher: Publisher | null = null;
  secondaryToken = '';
  isSecondaryCameraAvailable = false;

  // Device monitoring properties
  private deviceMonitorInterval: NodeJS.Timeout | null = null;
  private currentSecondaryDeviceId: string | null = null;
  private lastKnownVideoDeviceCount = 0;
  private isProcessingDeviceChange = false;

  constructor(
    private httpClient: HttpClient,
    private teleConsultService: TeleConsultService,
    private notificationService: ToastrService
  ) {}

  ngOnInit() {
    const OV = new OpenVidu();
    OV.getDevices().then(devices => {
      this.videoDevices = devices.filter(device => device.kind === 'videoinput');
      this.audioDevices = devices.filter(device => device.kind === 'audioinput');
      this.lastKnownVideoDeviceCount = this.videoDevices.length; // Initialize device count

      if (this.videoDevices.length > 0 || this.audioDevices.length > 0) {
        this.ovSettings = {
          chat: false,
          autopublish: true,
          toolbarButtons: {
            audio: true,
            video: true,
            screenShare: false,
            fullscreen: true,
            layoutSpeaking: false,
            exit: false,
          }
        };
      } else {
        this.notificationService.error('No video or audio devices have been found. Please connect at least one and refresh the page.');
      }

      this.joinSession();
      this.joinDeviceSession();

      if (this.videoDevices.length > 1) {
        this.initSecondaryCamera();
      }

      // Start device monitoring after a delay
      setTimeout(() => this.startDeviceMonitoring(), 10000);

      this.myUserName = this.participantName;
      console.log('Participant:' + this.participantName);
    });
  }

  ngOnDestroy() {
    this.stopDeviceMonitoring();

    if (this.secondaryPublisher && this.secondarySession) {
      this.secondarySession.unpublish(this.secondaryPublisher);
    }
    if (this.secondarySession) {
      this.secondarySession.disconnect();
    }
  }

  /**
   * Pauses device monitoring when the user clicks mute/video buttons.
   * This is the CORE FIX to prevent camera switching.
   */
  private pauseDeviceMonitoringOnUserAction(): void {
    console.log('🎤 User action (mic/video toggle) detected. Pausing device monitoring for 10 seconds.');
    this.stopDeviceMonitoring(); // Stop the interval completely
    // Restart monitoring after a safe delay
    setTimeout(() => {
      console.log('✅ Resuming device monitoring.');
      this.startDeviceMonitoring();
    }, 10000); // 10-second safe window
  }

  /**
   * Starts monitoring for physical device changes (camera connect/disconnect).
   */
  private startDeviceMonitoring(): void {
    // Ensure no existing interval is running
    this.stopDeviceMonitoring();

    this.deviceMonitorInterval = setInterval(async () => {
      if (this.isProcessingDeviceChange) {
        return; // Don't run if a change is already being processed
      }
      await this.checkDeviceChanges();
    }, 3000); // Check every 3 seconds
    console.log('Device monitoring started.');
  }

  /**
   * Stops device monitoring.
   */
  private stopDeviceMonitoring(): void {
    if (this.deviceMonitorInterval) {
      clearInterval(this.deviceMonitorInterval);
      this.deviceMonitorInterval = null;
      console.log('Device monitoring stopped.');
    }
  }

  /**
   * Checks for changes in the NUMBER of connected video devices.
   */
  private async checkDeviceChanges(): Promise<void> {
    this.isProcessingDeviceChange = true;
    try {
      const OV = new OpenVidu();
      const devices = await OV.getDevices();
      const currentVideoDevices = devices.filter(device => device.kind === 'videoinput');
      const currentVideoDeviceCount = currentVideoDevices.length;

      // CORE LOGIC: Only proceed if the number of cameras has actually changed.
      if (currentVideoDeviceCount === this.lastKnownVideoDeviceCount) {
        // console.log(`Camera count is stable at ${currentVideoDeviceCount}. Ignoring potential label/ID changes.`);
        this.isProcessingDeviceChange = false;
        return; // No change in count, so we do nothing. This prevents the bug.
      }

      console.log(`❗️ Camera count changed from ${this.lastKnownVideoDeviceCount} to ${currentVideoDeviceCount}. Processing change...`);
      this.videoDevices = currentVideoDevices; // Update the main device list

      // A camera was ADDED
      if (currentVideoDeviceCount > this.lastKnownVideoDeviceCount && !this.isSecondaryCameraAvailable) {
        console.log('New camera connected. Initializing as secondary camera.');
        this.initSecondaryCamera();
        this.notificationService.info('New camera detected and connected.');
      }
      // A camera was REMOVED
      else if (currentVideoDeviceCount < this.lastKnownVideoDeviceCount && this.isSecondaryCameraAvailable) {
        // Check if the disconnected camera was our secondary one
        const secondaryDeviceStillExists = currentVideoDevices.some(d => d.deviceId === this.currentSecondaryDeviceId);
        if (!secondaryDeviceStillExists) {
          console.log('Secondary camera disconnected.');
          await this.handleSecondaryCameraDisconnect();
          this.notificationService.info('Secondary camera disconnected.');
        }
      }

      this.lastKnownVideoDeviceCount = currentVideoDeviceCount; // Update the count for the next check
    } catch (error) {
      console.error('Error checking device changes:', error);
    } finally {
      this.isProcessingDeviceChange = false;
    }
  }

  /**
   * Initializes the secondary camera stream.
   */
  private initSecondaryCamera(): void {
    if (this.videoDevices.length < 2) {
      console.warn('Cannot init secondary camera, only one video device found.');
      return;
    }

    const usbCamera = this.findUSBCamera();
    if (usbCamera) {
      console.log('USB camera detected:', usbCamera.label);
      this.joinSecondaryCameraWithDevice(usbCamera.deviceId);
      this.isSecondaryCameraAvailable = true;
    } else {
      console.warn('No specific USB camera detected. Using fallback (second device in list).');
      this.joinSecondaryCameraWithDevice(this.videoDevices[1].deviceId);
      this.isSecondaryCameraAvailable = true;
    }
  }

  /**
   * Connects and publishes the secondary camera stream.
   * @param deviceId The deviceId of the camera to use.
   */
  private async joinSecondaryCameraWithDevice(deviceId: string): Promise<void> {
    try {
      // Disconnect any existing secondary stream before starting a new one
      await this.handleSecondaryCameraDisconnect();

      const response = await this.teleConsultService.getDeviceVideoToken(this.consultationId).toPromise();
      this.secondaryToken = response['token'];

      this.secondaryOV = new OpenVidu();
      this.secondarySession = this.secondaryOV.initSession();

      this.secondarySession.on('streamCreated', (event: any) => {
        if (event.stream && this.secondaryContainer?.nativeElement) {
          const subscriber = this.secondarySession.subscribe(event.stream, this.secondaryContainer.nativeElement);
          subscriber.on('videoElementCreated', (ev: any) => {
            if (ev.element) {
              this.secondaryContainer.nativeElement.appendChild(ev.element);
            }
          });
        }
      });

      await this.secondarySession.connect(this.secondaryToken, { clientData: this.participantName + '_secondary' });

      this.secondaryPublisher = this.secondaryOV.initPublisher(undefined, { // Publish to memory first
        videoSource: deviceId,
        publishAudio: false,
        publishVideo: true,
        resolution: '640x480',
        frameRate: 30,
        mirror: false
      });

      await this.secondarySession.publish(this.secondaryPublisher);
      this.currentSecondaryDeviceId = deviceId;
      console.log('Successfully published secondary camera stream with device:', deviceId);
    } catch (error) {
      console.error('Error streaming secondary camera:', error);
      this.isSecondaryCameraAvailable = false;
      this.currentSecondaryDeviceId = null;
    }
  }

  /**
   * Disconnects and cleans up the secondary camera stream.
   */
  private async handleSecondaryCameraDisconnect(): Promise<void> {
    if (this.secondarySession) {
        if (this.secondaryPublisher) {
            await this.secondarySession.unpublish(this.secondaryPublisher);
        }
        await this.secondarySession.disconnect();
    }
    this.secondaryPublisher = null;
    this.secondarySession = null;
    this.secondaryOV = null;
    this.isSecondaryCameraAvailable = false;
    this.currentSecondaryDeviceId = null;

    if (this.secondaryContainer?.nativeElement) {
      this.secondaryContainer.nativeElement.innerHTML = '';
    }
    console.log('Secondary camera session cleaned up.');
  }

  /**
   * Finds a suitable USB/External camera from the device list.
   */
  private findUSBCamera(): Device | undefined {
    // Prioritize devices with labels indicating they are external
    const externalKeywords = ['usb', 'external', 'webcam', 'logitech', 'microsoft', 'creative', 'genius'];
    let usbCamera = this.videoDevices.find(device => externalKeywords.some(keyword => device.label.toLowerCase().includes(keyword)));

    // If not found, try to find a camera that isn't built-in
    if (!usbCamera) {
      const internalKeywords = ['facetime', 'integrated', 'built-in', 'internal'];
      usbCamera = this.videoDevices.find(device => !internalKeywords.some(keyword => device.label.toLowerCase().includes(keyword)));
    }

    // As a final fallback, if there are more than 1 cameras and no clear winner, pick the second one
    if (!usbCamera && this.videoDevices.length > 1) {
      usbCamera = this.videoDevices[1];
    }
    return usbCamera;
  }

  // --- OpenVidu Session and Token Management Methods ---

  handlerPublisherCreatedEvent(publisher: Publisher): void {
    // Listen for the stream property change event, which fires after a mute/unmute action completes.
    publisher.on('streamPropertyChanged', (event: any) => {
      if (event.changedProperty === 'audioActive' || event.changedProperty === 'videoActive') {
        console.log(`Stream property changed: ${event.changedProperty}. Triggering device monitoring pause.`);
        this.pauseDeviceMonitoringOnUserAction();
      }
    });
  }

  onToolbarButtonClicked(event: any): void {
    console.log('Toolbar button clicked:', event.type);
    if (event.type === 'audio' || event.type === 'video') {
      // Proactively pause monitoring the moment the button is clicked.
      this.pauseDeviceMonitoringOnUserAction();
    }
  }

  joinSession() {
    this.teleConsultService.getVideoToken(this.consultationId).subscribe({
      next: (data) => {
        if (data['message']) {
          this.notificationService.error(data['message']);
        } else {
          this.videoToken = data['token'];
          this.sessionJoined = true;
          this.tokens.push(this.videoToken);
          this.session = true;
        }
      },
      error: (err) => {
        console.log('ERROR getting video token:', err);
        this.notificationService.error('Could not join session. Please refresh the page.');
      }
    });
  }

  joinDeviceSession() {
    this.teleConsultService.getDeviceVideoToken(this.consultationId).subscribe({
      next: (data) => {
        this.videoToken = data['token'];
        this.sessionJoined = true;
        this.deviceTokens.push(this.videoToken);
        this.session = true;
      },
      error: (err) => console.log('ERROR getting device token:', err)
    });
  }

  handlerSessionCreatedEvent(session: Session): void {
    session.on('sessionDisconnected', () => {
      this.session = false;
      this.tokens = [];
      this.showJoinButton.emit(false);
    });
  }

  handlerErrorEvent(event: any): void {
    console.error('OpenVidu error event:', event);
  }

  async getToken(): Promise<string> {
    const sessionId = await this.createSession(this.consultationId);
    return this.createToken(sessionId);
  }

  createSession(sessionId: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ customSessionId: sessionId });
      const options = { headers: new HttpHeaders({ Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET), 'Content-Type': 'application/json' }) };
      this.httpClient.post<any>(this.OPENVIDU_SERVER_URL + '/api/sessions', body, options).pipe(
        catchError((error) => {
          if (error.status === 409) resolve(sessionId);
          else reject(error);
          return observableThrowError(error);
        })
      ).subscribe({ next: (response) => resolve(response.id) });
    });
  }

  createToken(sessionId: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ session: sessionId });
      const options = { headers: new HttpHeaders({ Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET), 'Content-Type': 'application/json' }) };
      this.httpClient.post<any>(this.OPENVIDU_SERVER_URL + '/api/tokens', body, options).pipe(
        catchError((error) => {
          reject(error);
          return observableThrowError(error);
        })
      ).subscribe({ next: (response) => resolve(response.token) });
    });
  }
}