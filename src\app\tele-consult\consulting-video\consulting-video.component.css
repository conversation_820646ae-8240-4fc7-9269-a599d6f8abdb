#join {
  text-align: center;
}

#join,
#session {
  position: absolute;
  margin: auto;
  top: 100px;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  width: 70%;
}

/* Primary camera frame - locked to main session */
.primary-camera-frame {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Secondary camera frame - locked to secondary container */
.secondary-camera-frame {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 320px;
  height: 240px;
  z-index: 999;
  background-color: black;
  border: 2px solid #007bff;
  border-radius: 8px;
  overflow: hidden;
}

.secondary-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.secondary-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Legacy styles for backward compatibility */
.secondary-camera-container {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 320px;
  height: 240px;
  z-index: 999;
  background-color: black;
  border: 2px solid white;
  border-radius: 8px;
  overflow: hidden; 
}

.secondary-camera-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}